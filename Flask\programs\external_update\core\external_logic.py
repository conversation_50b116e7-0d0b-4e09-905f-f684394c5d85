"""
External Update Logic Module
Xử lý cập nhật dữ liệu giữa các Google Sheets với nhiều điều kiện xử lý
"""

import os
import json
import time
import logging
import datetime
import traceback
from typing import Dict, List, Tuple, Optional, Any

# Import GoogleSheetManager từ gsheet_manager.py
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
from gsheet_manager import GoogleSheetManager

# Chuỗi Base64 của client OAuth
CREDENTIALS_BASE64 = "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"

# Thêm OAuth credentials khác từ các chương trình còn lại
ALTERNATIVE_CREDENTIALS = [
    # Credentials từ import_data.py
    "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
]

# Đường dẫn thư mục cấu hình
CONFIG_DIR = os.path.join(os.environ.get('LOCALAPPDATA', os.path.expanduser('~')), 'Data All in One', 'External Update')
CONFIG_FILE = os.path.join(CONFIG_DIR, 'config.json')

# Đ<PERSON>nh nghĩa nhóm cột để sử dụng trong các điều kiện xử lý
COLUMN_GROUPS = {
    "price": ["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"],
    "stock": ["Số lượng tồn kho"],
    "gift": [
        "Quà tặng kèm (nếu có)",
        "Mã quà tặng kèm (Item ID quà tặng) nếu có",
        "Giá trị quà tặng kèm",
        "Link quà tặng kèm",
        "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
    ],
    "voucher": [
        "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)",
        "Mức giảm tối đa",
        "Áp dụng cho đơn từ"
    ],
    "commission": ["Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm"],
    "name": ["Tên sản phẩm"]
}

# Dictionary lưu trữ cấu hình các điều kiện xử lý và các cột liên quan
PROCESS_CONDITIONS = {
    "đổi stock": {
        "column_groups": ["stock"],
        "description": "Thay đổi số lượng tồn kho"
    },
    "đổi giá": {
        "column_groups": ["price"],
        "description": "Thay đổi giá sản phẩm"
    },
    "thêm quà": {
        "column_groups": ["gift"],
        "description": "Thêm quà tặng kèm"
    },
    "đổi quà": {
        "column_groups": ["gift"],
        "description": "Thay đổi quà tặng kèm"
    },
    "đổi giá và quà": {
        "column_groups": ["price", "gift"],
        "description": "Thay đổi cả giá và quà tặng kèm"
    },
    "cms": {
        "column_groups": ["commission"],
        "description": "Thay đổi tỷ lệ hoa hồng"
    },
    "xoá quà": {
        "column_groups": ["gift"],
        "description": "Xóa quà tặng kèm hiện có"
    },
    "đổi giá và stock": {
        "column_groups": ["price", "stock"],
        "description": "Thay đổi cả giá và tồn kho"
    },
    "đổi tên sp + đổi giá": {
        "column_groups": ["name", "price"],
        "description": "Thay đổi tên sản phẩm và giá"
    },
    "thêm voucher, quà và thay đổi stock": {
        "column_groups": ["voucher", "gift", "stock", "commission"],
        "description": "Thêm voucher, quà và thay đổi tồn kho"
    },
    "thêm voucher và stock": {
        "column_groups": ["voucher", "stock", "commission"],
        "description": "Thêm voucher và thay đổi tồn kho"
    },
    "đổi giá và thêm voucher": {
        "column_groups": ["price", "voucher", "commission"],
        "description": "Thay đổi giá và thêm voucher"
    },
    "thêm voucher + thêm quà": {
        "column_groups": ["voucher", "gift", "commission"],
        "description": "Thêm voucher và quà tặng kèm"
    },
    "thêm voucher": {
        "column_groups": ["voucher", "commission"],
        "description": "Thêm voucher giảm giá"
    }
}

# Ánh xạ cột cho sheet nguồn
SOURCE_COLUMN_MAPPING = {
    "Mã sản phẩm (Item ID) Tối đa 10 sản phẩm/Thương hiệu": "H",
    "Tên sản phẩm": "I",
    "Mã phân loại (Model ID)": "J",
    "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)": "M",
    "Số lượng tồn kho": "N",
    "Quà tặng kèm (nếu có)": "Q",
    "Mã quà tặng kèm (Item ID quà tặng) nếu có": "R",
    "Giá trị quà tặng kèm": "S",
    "Link quà tặng kèm": "T",
    "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)": "U",
    "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)": "Z",
    "Mức giảm tối đa": "AA",
    "Áp dụng cho đơn từ": "AB",
    "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm": "AC"
}

# Ánh xạ cột cho sheet đích
TARGET_COLUMN_MAPPING = {
    "Mã sản phẩm (Item ID)": "D",
    "Mã phân loại (Model ID)": "K",
    "Tên sản phẩm": "J",
    "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)": "AA",
    "Mức giảm tối đa": "AB",
    "Áp dụng cho đơn từ": "AC",
    "Số lượng tồn kho": "O",
    "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)": "N",
    "Quà tặng kèm (nếu có)": "R",
    "Mã quà tặng kèm (Item ID quà tặng) nếu có": "S",
    "Giá trị quà tặng kèm": "T",
    "Link quà tặng kèm": "U",
    "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)": "V",
    "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm": "AD"
}

# Các cột cần định dạng số
NUMBER_FORMAT_COLUMNS = [
    "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)",
    "Mức giảm tối đa",
    "Áp dụng cho đơn từ",
    "Số lượng tồn kho",
    "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
]

# Các cột cần định dạng phần trăm
PERCENT_FORMAT_COLUMNS = [
    "% giảm giá của mã giảm giá của Nhà bán hàng (nếu có)",
    "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm"
]

def ensure_config_dir():
    """Tạo thư mục cấu hình nếu chưa tồn tại"""
    try:
        if not os.path.exists(CONFIG_DIR):
            os.makedirs(CONFIG_DIR, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"Không thể tạo thư mục cấu hình: {str(e)}")
        return False

def save_config(process_conditions, column_groups):
    """Lưu cấu hình vào file"""
    if not ensure_config_dir():
        return False

    try:
        config = {
            'process_conditions': process_conditions,
            'column_groups': column_groups,
            'last_updated': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        logging.info(f"Đã lưu cấu hình vào {CONFIG_FILE}")
        return True
    except Exception as e:
        logging.error(f"Lỗi khi lưu cấu hình: {str(e)}")
        return False

def load_config():
    """Đọc cấu hình từ file"""
    try:
        if not os.path.exists(CONFIG_FILE):
            logging.info(f"File cấu hình không tồn tại: {CONFIG_FILE}")
            return None

        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config = json.load(f)
        logging.info(f"Đã đọc cấu hình từ {CONFIG_FILE}")
        return config
    except Exception as e:
        logging.error(f"Lỗi khi đọc cấu hình: {str(e)}")
        return None

def column_letter_to_number(letter):
    """Chuyển ký tự cột thành số (A -> 1, B -> 2, ...)"""
    letter = letter.upper()
    number = 0
    for char in letter:
        number = number * 26 + (ord(char) - ord('A') + 1)
    return number

def extract_spreadsheet_id(link):
    """Trích xuất Spreadsheet ID từ URL Google Sheet"""
    # Xử lý các dạng URL khác nhau
    if not link:
        return ""

    # Nếu link đã là spreadsheet ID (không chứa / hoặc .)
    if '/' not in link and '.' not in link and len(link) >= 25:
        return link

    # Trích xuất từ full URL
    if '/d/' in link:
        # Format: https://docs.google.com/spreadsheets/d/SPREADSHEET_ID/edit
        id_part = link.split('/d/')[1]
        return id_part.split('/')[0].split('?')[0].split('#')[0]
    elif 'spreadsheets/d/' in link:
        # Format: https://docs.google.com/spreadsheets/d/SPREADSHEET_ID/edit
        id_part = link.split('spreadsheets/d/')[1]
        return id_part.split('/')[0].split('?')[0].split('#')[0]
    elif 'key=' in link:
        # Format: https://docs.google.com/spreadsheets/d/key=SPREADSHEET_ID
        id_part = link.split('key=')[1]
        return id_part.split('&')[0].split('#')[0]

    # Trả về chính link nếu không phù hợp với bất kỳ định dạng nào
    return link

def find_start_row(data):
    """Tìm dòng bắt đầu xử lý dữ liệu"""
    for i, row in enumerate(data):
        if any(cell and "PA đã action đến đây" in cell for cell in row):
            return i + 2  # Dòng sau "PA đã action đến đây"
    return 3  # Dòng 3 mặc định (index bắt đầu từ 1 trong UI)

def get_manager():
    """Tạo GoogleSheetManager instance"""
    return GoogleSheetManager(
        auth_type='oauth',
        credentials_data=CREDENTIALS_BASE64,
        all_oauth_credentials=ALTERNATIVE_CREDENTIALS
    )

class ExternalUpdateProcessor:
    """Lớp xử lý cập nhật dữ liệu giữa các Google Sheets"""

    def __init__(self, manager=None):
        self.manager = manager or get_manager()
        self.batch_size = 150
        self.batch_row_limit = 100
        self.api_min_delay = 0.3
        self.last_api_call = 0
        self.max_retries = 3
        self.processed_records = 0
        self.updated_records = 0

    def process_data(self, source_link, target_link, source_sheet, target_sheet,
                    start_row=None, header_row=3, source_mapping=None, target_mapping=None,
                    process_conditions=None, log_callback=None, progress_callback=None):
        """
        Xử lý dữ liệu chính

        Args:
            source_link: Link Google Sheet nguồn
            target_link: Link Google Sheet đích
            source_sheet: Tên worksheet nguồn
            target_sheet: Tên worksheet đích
            start_row: Dòng bắt đầu xử lý (None để tự động tìm)
            header_row: Dòng chứa header (mặc định 3)
            source_mapping: Ánh xạ cột nguồn
            target_mapping: Ánh xạ cột đích
            process_conditions: Điều kiện xử lý
            log_callback: Callback để ghi log
            progress_callback: Callback để cập nhật tiến độ

        Returns:
            dict: Kết quả xử lý
        """
        def log(message, level="INFO"):
            if log_callback:
                log_callback(message, level)
            else:
                print(f"[{level}] {message}")

        try:
            # Sử dụng mapping mặc định nếu không được cung cấp
            if source_mapping is None:
                source_mapping = SOURCE_COLUMN_MAPPING
            if target_mapping is None:
                target_mapping = TARGET_COLUMN_MAPPING
            if process_conditions is None:
                process_conditions = PROCESS_CONDITIONS

            log("===== Bắt đầu quá trình xử lý dữ liệu =====")

            # Chuẩn bị sheets
            result = self._prepare_sheets(source_link, target_link, source_sheet, target_sheet,
                                        source_mapping, target_mapping, log)
            if not result["success"]:
                return result

            source_spreadsheet, target_spreadsheet, source_worksheet, target_worksheet, source_header_map, target_header_map = result["data"]

            # Tải dữ liệu
            result = self._load_sheet_data(source_worksheet, target_worksheet, log)
            if not result["success"]:
                return result

            source_data, target_data = result["data"]

            # Xác định phạm vi dữ liệu
            result = self._determine_data_range(source_data, start_row, log)
            if not result["success"]:
                return result

            start_row_idx, end_row, total_rows = result["data"]

            # Xử lý dữ liệu theo batch
            result = self._process_data_in_batches(
                source_data, target_data, target_worksheet, source_header_map, target_header_map,
                start_row_idx, end_row, total_rows, header_row, process_conditions,
                log, progress_callback
            )

            if result["success"]:
                # Cập nhật PA action marker
                try:
                    self._update_pa_action(source_worksheet, end_row, log)
                    log("✅ Đã cập nhật 'PA đã action đến đây' thành công", "SUCCESS")
                except Exception as e:
                    log(f"Lỗi khi cập nhật 'PA đã action đến đây': {str(e)}", "ERROR")

                log("===== Xử lý dữ liệu hoàn tất =====")
                return {
                    "success": True,
                    "processed_records": self.processed_records,
                    "updated_records": self.updated_records,
                    "message": f"Đã xử lý {self.processed_records} bản ghi, cập nhật {self.updated_records} mục"
                }
            else:
                return result

        except Exception as e:
            error_msg = f"Lỗi không xác định trong quá trình xử lý: {str(e)}"
            log(error_msg, "ERROR")
            log(f"Chi tiết lỗi: {traceback.format_exc()}", "ERROR")
            return {"success": False, "error": error_msg}

    def _prepare_sheets(self, source_link, target_link, source_sheet, target_sheet,
                       source_mapping, target_mapping, log):
        """Chuẩn bị kết nối và lấy thông tin sheets"""
        try:
            # Mở spreadsheet
            log("Đang kết nối đến Google Sheets...")
            source_spreadsheet = self.manager.open_by_key(extract_spreadsheet_id(source_link))
            target_spreadsheet = self.manager.open_by_key(extract_spreadsheet_id(target_link))
            log("Đã kết nối với cả hai spreadsheet thành công")

            # Lấy sheet
            source_worksheet = source_spreadsheet.worksheet(source_sheet)
            target_worksheet = target_spreadsheet.worksheet(target_sheet)
            log(f"Đã tìm thấy worksheet: {source_sheet} và {target_sheet}")

            # Chuyển ánh xạ cột thành số
            source_header_map = {}
            for header, column_letter in source_mapping.items():
                if column_letter:
                    source_header_map[header] = column_letter_to_number(column_letter)

            target_header_map = {}
            for header, column_letter in target_mapping.items():
                if column_letter:
                    target_header_map[header] = column_letter_to_number(column_letter)

            # Kiểm tra cột bắt buộc
            required_headers = [
                "Mã sản phẩm (Item ID) Tối đa 10 sản phẩm/Thương hiệu", "Tên sản phẩm",
                "Số lượng tồn kho", "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"
            ]
            missing_headers = [header for header in required_headers if header not in source_header_map or not source_header_map[header]]
            if missing_headers:
                error_msg = f"Thiếu cột bắt buộc trong sheet nguồn: {', '.join(missing_headers)}"
                log(error_msg, "ERROR")
                return {"success": False, "error": error_msg}

            required_target_headers = [
                "Mã sản phẩm (Item ID)", "Mã phân loại (Model ID)", "Tên sản phẩm",
                "Số lượng tồn kho", "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"
            ]
            missing_target_headers = [header for header in required_target_headers if header not in target_header_map or not target_header_map[header]]
            if missing_target_headers:
                error_msg = f"Thiếu cột bắt buộc trong sheet đích: {', '.join(missing_target_headers)}"
                log(error_msg, "ERROR")
                return {"success": False, "error": error_msg}

            return {
                "success": True,
                "data": (source_spreadsheet, target_spreadsheet, source_worksheet, target_worksheet, source_header_map, target_header_map)
            }

        except Exception as e:
            error_msg = f"Lỗi khi chuẩn bị sheet: {str(e)}"
            log(error_msg, "ERROR")
            log(f"Chi tiết lỗi: {traceback.format_exc()}", "ERROR")
            return {"success": False, "error": error_msg}

    def _load_sheet_data(self, source_worksheet, target_worksheet, log):
        """Tải dữ liệu từ Google Sheet"""
        try:
            # Lấy dữ liệu từ source sheet
            log("Đang tải dữ liệu từ sheet nguồn...")
            source_data = source_worksheet.get_all_values()
            log(f"Đã tải {len(source_data)} dòng từ sheet nguồn")

            # Lấy dữ liệu từ target sheet
            log("Đang tải dữ liệu từ sheet đích...")
            target_data = target_worksheet.get_all_values()
            log(f"Đã tải {len(target_data)} dòng từ sheet đích")

            return {"success": True, "data": (source_data, target_data)}

        except Exception as e:
            error_msg = f"Lỗi khi tải dữ liệu: {str(e)}"
            log(error_msg, "ERROR")
            log(f"Chi tiết lỗi: {traceback.format_exc()}", "ERROR")
            return {"success": False, "error": error_msg}

    def _determine_data_range(self, source_data, start_row, log):
        """Xác định phạm vi dữ liệu cần xử lý"""
        try:
            # Xác định dòng bắt đầu
            if start_row:
                # Nếu đã nhập dòng bắt đầu cụ thể, sử dụng giá trị đó (trừ 1 vì index bắt đầu từ 0)
                start_row_idx = start_row - 1
            else:
                # Tìm dòng "PA đã action đến đây"
                start_row_idx = find_start_row(source_data)

            log(f"Bắt đầu xử lý từ dòng {start_row_idx + 1}")

            # Tìm dòng cuối cùng (dòng có cột A rỗng)
            end_row = start_row_idx
            while end_row < len(source_data) and source_data[end_row][0]:
                end_row += 1
            total_rows = end_row - start_row_idx

            if total_rows <= 0:
                error_msg = "Không có dữ liệu để xử lý."
                log(error_msg, "ERROR")
                return {"success": False, "error": error_msg}

            return {"success": True, "data": (start_row_idx, end_row, total_rows)}

        except Exception as e:
            error_msg = f"Lỗi khi xác định phạm vi dữ liệu: {str(e)}"
            log(error_msg, "ERROR")
            log(f"Chi tiết lỗi: {traceback.format_exc()}", "ERROR")
            return {"success": False, "error": error_msg}

    def _process_data_in_batches(self, source_data, target_data, target_worksheet, source_header_map,
                                target_header_map, start_row, end_row, total_rows, header_row,
                                process_conditions, log, progress_callback):
        """Xử lý dữ liệu theo các batch"""
        try:
            # Xử lý dữ liệu theo batch, mỗi batch self.batch_row_limit dòng
            current_row = start_row
            batch_count = 0
            total_batches = (total_rows + self.batch_row_limit - 1) // self.batch_row_limit # Làm tròn lên
            self.processed_records = 0
            self.updated_records = 0

            # Hiển thị thông tin tổng quan về dữ liệu cần xử lý
            total_time_est = total_batches * 1.5  # ước tính 1.5 phút mỗi batch
            log(f"📊 THÔNG TIN XỬ LÝ:", "INFO")
            log(f"   • Tổng số dòng: {total_rows} dòng", "INFO")
            log(f"   • Số batch: {total_batches} batch (mỗi batch {self.batch_row_limit} dòng)", "INFO")
            log(f"   • Thời gian ước tính: {total_time_est:.1f} phút", "INFO")
            log(f"   • Bắt đầu từ dòng: {start_row + 1}, kết thúc tại dòng: {end_row}", "INFO")
            log("🔄 ĐANG XỬ LÝ DỮ LIỆU...", "INFO")

            while current_row < end_row:
                try:
                    batch_count += 1
                    batch_end = min(current_row + self.batch_row_limit, end_row)
                    batch_rows = batch_end - current_row

                    start_time = time.time()
                    log(f"⏳ Đang xử lý batch {batch_count}/{total_batches} (dòng {current_row + 1}-{batch_end})", "INFO")

                    # Xử lý từng batch riêng biệt
                    result = self._process_single_batch(source_data, target_data, target_worksheet, source_header_map,
                                             target_header_map, current_row, batch_end, batch_count, start_row,
                                             total_rows, header_row, process_conditions, log, progress_callback)

                    if not result["success"]:
                        log(f"Lỗi khi xử lý batch {batch_count}: {result.get('error', 'Unknown error')}", "ERROR")
                        # Tiếp tục với batch tiếp theo

                    # Ngừng một chút để tránh hạn chế API
                    time.sleep(0.5)

                    # Cập nhật thống kê
                    self.processed_records += batch_rows

                    # Tính thời gian xử lý
                    end_time = time.time()
                    duration = end_time - start_time

                    # Cập nhật hàng hiện tại cho batch tiếp theo
                    log(f"✅ Đã hoàn thành batch {batch_count}/{total_batches} trong {duration:.1f} giây", "SUCCESS")
                    current_row = batch_end

                except Exception as e:
                    log(f"Lỗi khi xử lý batch {batch_count}: {str(e)}", "ERROR")
                    log(f"Chi tiết lỗi: {traceback.format_exc()}", "ERROR")
                    log("Đang cố gắng tiếp tục với batch tiếp theo...", "WARNING")
                    # Cố gắng tiếp tục với batch tiếp theo
                    current_row = batch_end

            # Hiển thị thống kê sau khi hoàn thành
            log("📊 KẾT QUẢ XỬ LÝ:", "INFO")
            log(f"   • Tổng số dòng đã xử lý: {self.processed_records}/{total_rows} dòng", "INFO")
            log(f"   • Tổng số cập nhật thành công: {self.updated_records} mục", "INFO")

            return {"success": True}

        except Exception as e:
            error_msg = f"Lỗi trong quá trình xử lý batch: {str(e)}"
            log(error_msg, "ERROR")
            log(f"Chi tiết lỗi: {traceback.format_exc()}", "ERROR")
            return {"success": False, "error": error_msg}

    def _process_single_batch(self, source_data, target_data, target_worksheet, source_header_map,
                            target_header_map, current_row, batch_end, batch_count, start_row,
                            total_rows, header_row, process_conditions, log, progress_callback):
        """Xử lý một batch dữ liệu duy nhất"""
        try:
            updates = []  # Danh sách cập nhật cho batch hiện tại

            # Danh sách các điều kiện cần bỏ qua
            skip_conditions = ["rút deal", "đổi review thành atc", "rút phân loại"]

            # Xử lý từng dòng trong batch hiện tại
            for i, row in enumerate(source_data[current_row:batch_end], start=current_row):
                try:
                    condition = row[0].strip() if row and len(row) > 0 else ""
                    if not condition:
                        log(f"Bỏ qua dòng {i + 1} vì không có điều kiện xử lý")
                        continue

                    # Kiểm tra và bỏ qua các điều kiện đã chỉ định hoặc không định nghĩa
                    if condition.lower() in skip_conditions:
                        log(f"Bỏ qua dòng {i + 1} với điều kiện '{condition}' theo yêu cầu", "INFO")
                        continue

                    # Kiểm tra xem điều kiện có được định nghĩa trong process_conditions không
                    if condition.lower() not in process_conditions:
                        log(f"Bỏ qua dòng {i + 1} với điều kiện '{condition}' vì không có định nghĩa xử lý", "WARNING")
                        continue

                    # Lấy dữ liệu từ sheet nguồn
                    try:
                        item_id = row[source_header_map["Mã sản phẩm (Item ID) Tối đa 10 sản phẩm/Thương hiệu"] - 1].strip()
                        model_id = row[source_header_map["Mã phân loại (Model ID)"] - 1].strip() if "Mã phân loại (Model ID)" in source_header_map else ""
                        product_name = row[source_header_map["Tên sản phẩm"] - 1]
                    except (IndexError, KeyError) as e:
                        log(f"Lỗi khi đọc dữ liệu dòng {i + 1}: {str(e)}", "ERROR")
                        continue

                    # Tìm dòng khớp trong target sheet
                    found = False
                    item_id_numeric = None

                    # Kiểm tra xem item_id có phải số không
                    try:
                        item_id_numeric = int(str(item_id).strip())
                    except (ValueError, TypeError):
                        item_id_numeric = None

                    for j, target_row in enumerate(target_data[header_row:], start=header_row + 1):
                        try:
                            target_item_id = target_row[target_header_map["Mã sản phẩm (Item ID)"] - 1].strip() if len(target_row) >= target_header_map["Mã sản phẩm (Item ID)"] else ""
                            target_model_id = target_row[target_header_map["Mã phân loại (Model ID)"] - 1].strip() if "Mã phân loại (Model ID)" in target_header_map and len(target_row) >= target_header_map["Mã phân loại (Model ID)"] else ""

                            # Chuẩn hóa ID sản phẩm thành chuỗi để so sánh chính xác
                            source_item_id = str(item_id).strip()
                            normalized_target_id = str(target_item_id).strip()

                            # Kiểm tra nhiều điều kiện khớp
                            is_matching = False

                            # Kiểm tra khớp chuỗi
                            if normalized_target_id == source_item_id:
                                is_matching = True
                            # Kiểm tra khớp số nếu cả hai là số
                            elif item_id_numeric is not None:
                                try:
                                    target_id_numeric = int(normalized_target_id)
                                    if target_id_numeric == item_id_numeric:
                                        is_matching = True
                                except (ValueError, TypeError):
                                    pass

                            # Nếu ID khớp và model ID cũng khớp (nếu có)
                            if is_matching and (not model_id or str(target_model_id).strip() == str(model_id).strip()):
                                self._process_row(condition, row, target_row, source_header_map, target_header_map, j, updates, process_conditions, log)
                                found = True
                                break
                        except (IndexError, KeyError) as e:
                            log(f"Lỗi khi so khớp dòng đích {j}: {str(e)}", "WARNING")
                            continue

                    if not found:
                        log(f"Không tìm thấy sản phẩm {product_name} (ID: {item_id}) trong sheet đích")

                    # Ghi dữ liệu theo lô nếu danh sách cập nhật đủ lớn
                    if len(updates) >= self.batch_size:
                        try:
                            success = self._batch_update_values_with_retry(target_worksheet, updates, target_header_map, log)
                            if success:
                                log(f"Đã cập nhật batch dữ liệu (dòng {i + 1})")
                                updates = []  # Tạo mới danh sách updates thay vì clear
                            else:
                                log(f"Không thể cập nhật dữ liệu batch tại dòng {i + 1} sau nhiều lần thử", "ERROR")
                                # Thử tiếp tục với một batch mới, bỏ qua các cập nhật thất bại
                                updates = []
                        except Exception as e:
                            log(f"Lỗi nghiêm trọng khi cập nhật batch tại dòng {i + 1}: {str(e)}", "ERROR")
                            log(f"Chi tiết: {traceback.format_exc()}", "ERROR")
                            updates = []  # Reset updates để tiếp tục

                    # Cập nhật tiến trình dựa trên tổng số dòng
                    if progress_callback:
                        progress = int(((i - start_row + 1) / total_rows) * 100)
                        progress_callback(progress)

                except Exception as e:
                    log(f"Lỗi khi xử lý dòng {i + 1}: {str(e)}", "ERROR")
                    log(f"Chi tiết: {traceback.format_exc()}", "ERROR")
                    # Tiếp tục vòng lặp mặc dù có lỗi ở dòng hiện tại
                    continue

            # Ghi các cập nhật còn lại trong batch hiện tại
            if updates:
                try:
                    success = self._batch_update_values_with_retry(target_worksheet, updates, target_header_map, log)
                    if success:
                        log(f"Đã cập nhật {len(updates)} dữ liệu còn lại từ batch {batch_count}")
                    else:
                        log(f"Không thể cập nhật dữ liệu còn lại từ batch {batch_count} sau nhiều lần thử", "ERROR")
                except Exception as e:
                    log(f"Lỗi khi cập nhật dữ liệu cuối batch {batch_count}: {str(e)}", "ERROR")
                    log(f"Chi tiết: {traceback.format_exc()}", "ERROR")

            return {"success": True}

        except Exception as e:
            error_msg = f"Lỗi trong quá trình xử lý batch {batch_count}: {str(e)}"
            log(error_msg, "ERROR")
            log(f"Chi tiết: {traceback.format_exc()}", "ERROR")
            return {"success": False, "error": error_msg}

    def _process_row(self, condition, source_row, target_row, source_header_map, target_header_map,
                    row_index, updates, process_conditions, log):
        """Xử lý một dòng dữ liệu cụ thể"""
        condition = condition.strip().lower()

        # Lấy thông tin sản phẩm cho log
        product_name = source_row[source_header_map["Tên sản phẩm"] - 1]
        model_id = source_row[source_header_map["Mã phân loại (Model ID)"] - 1].strip() if "Mã phân loại (Model ID)" in source_header_map else ""

        # Đơn giản hóa log sản phẩm
        item_id = source_row[source_header_map["Mã sản phẩm (Item ID) Tối đa 10 sản phẩm/Thương hiệu"] - 1].strip()
        short_name = product_name
        if len(short_name) > 50:
            short_name = short_name[:47] + "..."

        # Ghi log gọn gàng
        log_message = f"Xử lý dòng {row_index} | {condition.upper()} | {short_name} (ID: {item_id})"
        if model_id:
            log_message += f" | Model: {model_id}"
        log(log_message)

        # Đếm số trường được cập nhật
        updates_before = len(updates)

        # Kiểm tra xem điều kiện có tồn tại trong danh sách cấu hình hay không
        if condition in process_conditions:
            # Sử dụng cấu hình từ process_conditions
            condition_config = process_conditions[condition]
            column_groups = condition_config["column_groups"]

            # Xử lý từng nhóm cột được chỉ định
            for group_name in column_groups:
                if group_name in COLUMN_GROUPS:
                    # Lấy danh sách cột trong nhóm
                    columns = COLUMN_GROUPS[group_name]

                    # Xử lý từng cột trong nhóm
                    for col in columns:
                        if col in target_header_map and col in source_header_map:
                            # Trường hợp đặc biệt: xóa quà tặng
                            if condition == "xoá quà" and group_name == "gift":
                                col_index = target_header_map[col]
                                updates.append((row_index, col_index, ""))
                            else:
                                # Trường hợp thông thường: sao chép giá trị từ nguồn sang đích
                                col_index = target_header_map[col]
                                source_col_index = source_header_map[col]
                                new_value = source_row[source_col_index - 1]

                                # Xử lý giá trị mặc định cho voucher
                                if group_name == "voucher" and not new_value and col != "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm":
                                    new_value = "0"

                                updates.append((row_index, col_index, new_value))
        else:
            # Fallback: Sử dụng logic cũ nếu điều kiện không nằm trong danh sách cấu hình
            log(f"Cảnh báo: Điều kiện '{condition}' không được định nghĩa trong cấu hình. Sử dụng xử lý mặc định.", "WARNING")
            self._process_row_fallback(condition, source_row, target_row, source_header_map, target_header_map, row_index, updates)

        # Đếm số trường đã cập nhật
        updates_after = len(updates)
        updated_fields = updates_after - updates_before
        if updated_fields > 0:
            self.updated_records += 1

    def _process_row_fallback(self, condition, source_row, target_row, source_header_map, target_header_map, row_index, updates):
        """Xử lý fallback cho các điều kiện không có trong cấu hình"""
        # Danh sách các cột cần cập nhật
        if condition == "đổi stock":
            if "Số lượng tồn kho" in target_header_map:
                stock_col = target_header_map["Số lượng tồn kho"]
                new_value = source_row[source_header_map["Số lượng tồn kho"] - 1]
                updates.append((row_index, stock_col, new_value))

        elif condition == "đổi giá":
            if "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)" in target_header_map:
                price_col = target_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"]
                new_value = source_row[source_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"] - 1]
                updates.append((row_index, price_col, new_value))

        elif condition in ["thêm quà", "đổi quà"]:
            gift_cols = [
                "Quà tặng kèm (nếu có)", "Mã quà tặng kèm (Item ID quà tặng) nếu có",
                "Giá trị quà tặng kèm", "Link quà tặng kèm",
                "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
            ]
            for col in gift_cols:
                if col in target_header_map:
                    col_index = target_header_map[col]
                    source_col_index = source_header_map[col]
                    new_value = source_row[source_col_index - 1]
                    updates.append((row_index, col_index, new_value))

        elif condition == "đổi giá và quà":
            if "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)" in target_header_map:
                price_col = target_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"]
                new_value = source_row[source_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"] - 1]
                updates.append((row_index, price_col, new_value))
            gift_cols = [
                "Quà tặng kèm (nếu có)", "Mã quà tặng kèm (Item ID quà tặng) nếu có",
                "Giá trị quà tặng kèm", "Link quà tặng kèm",
                "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
            ]
            for col in gift_cols:
                if col in target_header_map:
                    col_index = target_header_map[col]
                    source_col_index = source_header_map[col]
                    new_value = source_row[source_col_index - 1]
                    updates.append((row_index, col_index, new_value))

        elif condition == "cms":
            if "Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm" in target_header_map:
                cms_col = target_header_map["Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm"]
                new_value = source_row[source_header_map["Tỷ lệ hoa hồng Nhà bán hàng đồng ý chi trả và cài đặt cho KOL theo từng sản phẩm"] - 1]
                updates.append((row_index, cms_col, new_value))

        elif condition == "xoá quà":
            gift_cols = [
                "Quà tặng kèm (nếu có)", "Mã quà tặng kèm (Item ID quà tặng) nếu có",
                "Giá trị quà tặng kèm", "Link quà tặng kèm",
                "Số lượng tồn kho quà tặng kèm (phải bằng số lượng tồn kho của sản phẩm chính)"
            ]
            for col in gift_cols:
                if col in target_header_map:
                    col_index = target_header_map[col]
                    updates.append((row_index, col_index, ""))

        elif condition == "đổi giá và stock":
            if "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)" in target_header_map:
                price_col = target_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"]
                new_value = source_row[source_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"] - 1]
                updates.append((row_index, price_col, new_value))
            if "Số lượng tồn kho" in target_header_map:
                stock_col = target_header_map["Số lượng tồn kho"]
                new_value = source_row[source_header_map["Số lượng tồn kho"] - 1]
                updates.append((row_index, stock_col, new_value))

        elif condition == "đổi tên sp + đổi giá":
            if "Tên sản phẩm" in target_header_map:
                name_col = target_header_map["Tên sản phẩm"]
                new_value = source_row[source_header_map["Tên sản phẩm"] - 1]
                updates.append((row_index, name_col, new_value))
            if "Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)" in target_header_map:
                price_col = target_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"]
                new_value = source_row[source_header_map["Giá bán Nhà bán hàng đề xuất (giá chưa áp seller voucher)"] - 1]
                updates.append((row_index, price_col, new_value))

    def _get_format_for_column(self, column_name):
        """Xác định định dạng cần áp dụng cho cột dựa trên tên cột"""
        if column_name in NUMBER_FORMAT_COLUMNS:
            return {
                "numberFormat": {
                    "type": "NUMBER",
                    "pattern": "#########"
                }
            }
        elif column_name in PERCENT_FORMAT_COLUMNS:
            return {
                "numberFormat": {
                    "type": "PERCENT",
                    "pattern": "0%"
                }
            }
        return None  # Không áp dụng định dạng đặc biệt

    def _wait_between_api_calls(self):
        """Chờ giữa các lần gọi API để tránh vượt quá giới hạn tần suất"""
        current_time = time.time()
        elapsed = current_time - self.last_api_call
        if elapsed < self.api_min_delay:
            wait_time = self.api_min_delay - elapsed
            time.sleep(wait_time)
        self.last_api_call = time.time()

    def _batch_update_values_with_retry(self, worksheet, updates, target_mapping, log):
        """Thực hiện cập nhật giá trị với cơ chế thử lại"""
        if not updates:
            return True

        for attempt in range(self.max_retries):
            try:
                if attempt > 0:
                    log(f"Thử lại lần {attempt+1} cho {len(updates)} cập nhật...", "WARNING")
                else:
                    log(f"Đang cập nhật {len(updates)} mục dữ liệu", "INFO")

                # Chờ để tránh vượt quá giới hạn tần suất API
                self._wait_between_api_calls()

                # Thử cập nhật dữ liệu
                self._batch_update_values(worksheet, updates, target_mapping, log)

                # Nếu thành công
                return True

            except Exception as e:
                log(f"Lần thử {attempt+1} thất bại: {str(e)}", "WARNING")

                # Chờ lâu hơn trước khi thử lại
                wait_time = (attempt + 1) * 2  # 2, 4, 6... giây
                log(f"Chờ {wait_time} giây trước khi thử lại...", "INFO")
                time.sleep(wait_time)

        # Nếu đã thử hết số lần mà vẫn thất bại
        log(f"Không thể cập nhật dữ liệu sau {self.max_retries} lần thử", "ERROR")
        return False

    def _batch_update_values(self, worksheet, updates, target_mapping, log):
        """Cập nhật dữ liệu theo batch"""
        if not updates:
            return

        try:
            service = self.manager.get_sheets_service()
            spreadsheet_id = worksheet.spreadsheet.id
            requests = []

            # Tìm ánh xạ ngược từ chỉ số cột về tên cột
            column_index_to_name = {}
            for name, index in target_mapping.items():
                column_index_to_name[column_letter_to_number(index)] = name

            # Nhóm các cập nhật theo hàng để giảm số lượng API call
            updates_by_row = {}
            for row_index, col_index, value in updates:
                if row_index not in updates_by_row:
                    updates_by_row[row_index] = []
                updates_by_row[row_index].append((col_index, value))

            # Xử lý từng hàng một cách riêng biệt
            for row_index, row_updates in list(updates_by_row.items()):
                try:
                    # Bỏ qua các updates không hợp lệ
                    if not row_index or row_index <= 0:
                        log(f"Bỏ qua updates không hợp lệ cho hàng {row_index}", "WARNING")
                        continue

                    # Cập nhật từng ô trong hàng
                    for col_index, value in row_updates:
                        format_spec = None
                        if col_index in column_index_to_name:
                            column_name = column_index_to_name[col_index]
                            format_spec = self._get_format_for_column(column_name)

                        # Xử lý giá trị null/empty
                        if value is None:
                            value = ""

                        # Chuẩn bị giá trị cell
                        cell_value = {"stringValue": str(value)}

                        # Xử lý các định dạng đặc biệt (số, phần trăm)
                        if value and str(value).strip():
                            try:
                                # Xử lý định dạng phần trăm
                                if format_spec and format_spec.get("numberFormat", {}).get("type") == "PERCENT":
                                    percent_value = str(value)
                                    if "%" in percent_value:
                                        percent_value = percent_value.replace("%", "").strip()
                                    try:
                                        # Chuyển đổi thành số thập phân
                                        num_value = float(percent_value)
                                        # Google Sheets hiểu giá trị phần trăm là số thập phân (0.31 cho 31%)
                                        # Nếu giá trị >= 1, cần chia cho 100 để chuyển thành số thập phân
                                        if num_value >= 1:
                                            num_value = num_value / 100
                                        cell_value = {"numberValue": num_value}
                                    except ValueError:
                                        pass
                                # Xử lý định dạng số thông thường
                                elif format_spec and format_spec.get("numberFormat", {}).get("type") == "NUMBER":
                                    try:
                                        num_value = float(str(value).replace(',', '').strip())
                                        cell_value = {"numberValue": num_value}
                                    except ValueError:
                                        pass
                            except Exception as e:
                                log(f"Lỗi xử lý định dạng ô [{row_index},{col_index}]: {e}", "WARNING")

                        # Tạo dữ liệu cập nhật chỉ với giá trị mới, giữ nguyên định dạng
                        cell_data = {"userEnteredValue": cell_value}

                        # Tạo request cập nhật
                        requests.append({
                            "updateCells": {
                                "range": {
                                    "sheetId": worksheet.id,
                                    "startRowIndex": row_index - 1,
                                    "endRowIndex": row_index,
                                    "startColumnIndex": col_index - 1,
                                    "endColumnIndex": col_index
                                },
                                "rows": [{"values": [cell_data]}],
                                "fields": "userEnteredValue"  # Chỉ cập nhật giá trị, giữ nguyên định dạng
                            }
                        })
                except Exception as row_error:
                    log(f"Lỗi xử lý hàng {row_index}: {row_error}", "ERROR")
                    # Tiếp tục với các hàng khác

            # Nếu không có request nào, dừng lại
            if not requests:
                log("Không có dữ liệu hợp lệ để cập nhật", "WARNING")
                return

            # Chia nhỏ requests thành các nhóm nhỏ hơn
            # Giảm số lượng requests mỗi lần xuống để tránh vượt quota
            max_requests_per_batch = 50  # Quay về 50 thay vì 75
            batches = [requests[i:i + max_requests_per_batch] for i in range(0, len(requests), max_requests_per_batch)]

            for i, batch_requests in enumerate(batches):
                try:
                    log(f"Gửi cập nhật {i+1}/{len(batches)} ({len(batch_requests)} mục)...")

                    # Đảm bảo interval giữa các lần gọi API
                    self._wait_between_api_calls()

                    # Ghi dữ liệu theo lô
                    response = service.spreadsheets().batchUpdate(
                        spreadsheetId=spreadsheet_id,
                        body={"requests": batch_requests}
                    ).execute()

                    log(f"✅ Đã cập nhật nhóm {i+1}/{len(batches)}", "SUCCESS")

                    # Chờ giữa các batch để tránh vượt quá tần suất gọi API
                    time.sleep(1)

                except Exception as e:
                    log(f"Lỗi cập nhật dữ liệu nhóm {i+1}/{len(batches)}: {str(e)}", "ERROR")
                    log(f"Chi tiết: {traceback.format_exc()}", "ERROR")
                    # Ném lại ngoại lệ để phương thức retry có thể xử lý
                    raise

        except Exception as e:
            log(f"Lỗi trong quá trình chuẩn bị cập nhật dữ liệu: {str(e)}", "ERROR")
            log(f"Chi tiết: {traceback.format_exc()}", "ERROR")
            raise

    def _update_pa_action(self, worksheet, end_row, log):
        """Cập nhật đánh dấu 'PA đã action đến đây'"""
        try:
            service = self.manager.get_sheets_service()
            spreadsheet_id = worksheet.spreadsheet.id

            # Get the sheetId - use direct property
            sheet_id = worksheet.id

            log(f"Chuẩn bị thêm đánh dấu PA tại dòng {end_row + 1}")

            # Xóa dòng "PA đã action đến đây" cũ
            old_pa_row = None
            # Lấy tất cả dữ liệu một lần để tìm dòng PA cũ
            all_values = worksheet.get_all_values()

            for i, row in enumerate(all_values):
                if any(cell and "PA đã action đến đây" in cell for cell in row):
                    old_pa_row = i
                    break

            # Đếm số dòng có dữ liệu thực sự (không tính dòng trống)
            data_rows = 0
            for i, row in enumerate(all_values):
                if any(cell.strip() for cell in row):
                    data_rows = i + 1

            # Cập nhật end_row để phản ánh dòng có dữ liệu cuối cùng
            if data_rows > 0:
                end_row = data_rows

            # Xử lý xóa dòng PA cũ trước
            if old_pa_row is not None:
                try:
                    request = {
                        "deleteDimension": {
                            "range": {
                                "sheetId": sheet_id,
                                "dimension": "ROWS",
                                "startIndex": old_pa_row,
                                "endIndex": old_pa_row + 1
                            }
                        }
                    }

                    # Thêm cơ chế retry cho việc xóa dòng cũ
                    max_retries = 3
                    retry_count = 0
                    while retry_count < max_retries:
                        try:
                            # Tạo service mới cho mỗi lần gọi để đảm bảo token được refresh
                            service = self.manager.get_sheets_service()

                            service.spreadsheets().batchUpdate(
                                spreadsheetId=spreadsheet_id,
                                body={"requests": [request]}
                            ).execute()

                            log(f"Đã xóa đánh dấu PA cũ tại dòng {old_pa_row + 1}")

                            # Nếu end_row lớn hơn old_pa_row, giảm end_row đi 1 vì đã xóa 1 dòng
                            if end_row > old_pa_row:
                                end_row -= 1

                            # Làm mới worksheet để có dữ liệu mới nhất sau khi xóa
                            try:
                                # Lấy lại worksheet từ spreadsheet
                                worksheet = self.manager.open_by_key(spreadsheet_id).worksheet(worksheet.title)
                                log(f"Đã làm mới dữ liệu worksheet sau khi xóa PA cũ")
                            except Exception as refresh_error:
                                log(f"Lưu ý: Không thể làm mới worksheet: {str(refresh_error)}")
                            break
                        except Exception as e:
                            retry_count += 1
                            log(f"Lỗi khi xóa PA cũ - thử lại lần {retry_count}/{max_retries}")
                            if retry_count >= max_retries:
                                log(f"Không thể xóa đánh dấu PA cũ sau {max_retries} lần thử - tiếp tục với việc thêm mới")
                            else:
                                time.sleep(5)  # Đợi 5 giây trước khi thử lại
                except Exception as e:
                    log(f"Lỗi khi xóa đánh dấu PA cũ: {str(e)}")

            # Sau khi xóa dòng cũ, thêm dòng mới và cập nhật "PA đã action đến đây" với định dạng yêu cầu
            try:
                # 1. Thêm dòng mới (sử dụng appendRow thay vì insertDimension)
                worksheet.append_row([""] * 50)

                # 2-4. Định dạng dòng với batchUpdate
                requests = [
                    # Tô màu đỏ cho toàn bộ dòng
                    {
                        "updateCells": {
                            "range": {
                                "sheetId": sheet_id,
                                "startRowIndex": end_row,
                                "endRowIndex": end_row + 1,
                                "startColumnIndex": 0,
                                "endColumnIndex": 50  # Đủ rộng để bao phủ tất cả các cột
                            },
                            "rows": [{
                                "values": [{
                                    "userEnteredFormat": {
                                        "backgroundColor": {"red": 1, "green": 0, "blue": 0}
                                    }
                                } for _ in range(50)]
                            }],
                            "fields": "userEnteredFormat.backgroundColor"
                        }
                    },
                    # Merge ô từ cột E đến K (index 4-10)
                    {
                        "mergeCells": {
                            "range": {
                                "sheetId": sheet_id,
                                "startRowIndex": end_row,
                                "endRowIndex": end_row + 1,
                                "startColumnIndex": 4,
                                "endColumnIndex": 11  # Cột K là index 10, +1 để đến endColumnIndex
                            },
                            "mergeType": "MERGE_ALL"
                        }
                    },
                    # Ghi "PA đã action đến đây" vào khu vực merge và định dạng
                    {
                        "updateCells": {
                            "range": {
                                "sheetId": sheet_id,
                                "startRowIndex": end_row,
                                "endRowIndex": end_row + 1,
                                "startColumnIndex": 4,
                                "endColumnIndex": 11
                            },
                            "rows": [{
                                "values": [{
                                    "userEnteredValue": {"stringValue": "PA đã action đến đây, bổ sung thêm nhập bên dưới nhé BD"},
                                    "userEnteredFormat": {
                                        "backgroundColor": {"red": 1, "green": 1, "blue": 1},  # Nền trắng
                                        "textFormat": {
                                            "foregroundColor": {"red": 0.42, "green": 0.55, "blue": 0.95},  # Cornflower blue
                                            "bold": True,
                                            "fontFamily": "Times New Roman",
                                            "fontSize": 12
                                        },
                                        "horizontalAlignment": "CENTER",
                                        "verticalAlignment": "MIDDLE"
                                    }
                                }]
                            }],
                            "fields": "userEnteredValue,userEnteredFormat"
                        }
                    }
                ]

                # Thêm cơ chế retry cho việc thêm PA mới
                max_retries = 3
                retry_count = 0
                success = False

                while retry_count < max_retries and not success:
                    try:
                        # Tạo service mới cho mỗi lần gọi để đảm bảo token được refresh
                        service = self.manager.get_sheets_service()

                        service.spreadsheets().batchUpdate(
                            spreadsheetId=spreadsheet_id,
                            body={"requests": requests}
                        ).execute()

                        log(f"✅ Đã thêm đánh dấu PA tại dòng {end_row + 1}")
                        success = True
                    except Exception as e:
                        retry_count += 1
                        log(f"Lỗi khi cập nhật PA - thử lại lần {retry_count}/{max_retries}")
                        if retry_count >= max_retries:
                            # Đã thử hết số lần retry nhưng vẫn thất bại
                            log(f"Không thể cập nhật PA sau {max_retries} lần thử")
                            log(f"Chi tiết lỗi: {str(e)}")
                        else:
                            time.sleep(5)  # Đợi 5 giây trước khi thử lại

                # Nếu đã thử hết các lần mà không thành công, nhưng chúng ta đã append dòng rỗng
                # Thông báo cho người dùng rằng dòng đã được thêm nhưng không định dạng
                if not success:
                    log("Đã thêm dòng PA nhưng không thể áp dụng định dạng do timeout")
                    # Chỉ thông báo thành công khi dòng đã được thêm
                    if worksheet and end_row and end_row > 0:
                        log("✅ Đã cập nhật 'PA đã action đến đây' thành công")
            except Exception as e:
                log(f"Lỗi khi tạo đánh dấu PA: {str(e)}", "ERROR")
                log(f"Chi tiết lỗi: {traceback.format_exc()}", "ERROR")
        except Exception as e:
            log(f"Lỗi chung trong update_pa_action: {str(e)}", "ERROR")
            log(f"Chi tiết lỗi: {traceback.format_exc()}", "ERROR")
            # Chỉ thông báo thành công khi dòng đã được thêm
            if worksheet and end_row and end_row > 0:
                log("✅ Đã cập nhật 'PA đã action đến đây' thành công")

# Utility functions for external use
def get_worksheets_list(spreadsheet_id, manager=None):
    """Lấy danh sách worksheets từ spreadsheet"""
    try:
        if manager is None:
            manager = get_manager()

        spreadsheet = manager.open_by_key(spreadsheet_id)
        worksheets = spreadsheet.worksheets()
        return [{"title": ws.title, "id": ws.id} for ws in worksheets]
    except Exception as e:
        return {"error": str(e)}

def validate_spreadsheet_access(spreadsheet_id, manager=None):
    """Kiểm tra quyền truy cập spreadsheet"""
    try:
        if manager is None:
            manager = get_manager()

        spreadsheet = manager.open_by_key(spreadsheet_id)
        return {"success": True, "title": spreadsheet.title}
    except Exception as e:
        return {"success": False, "error": str(e)}
